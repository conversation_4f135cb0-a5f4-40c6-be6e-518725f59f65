<?php

use App\classes\ClientePotencialServicio;

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = $_POST['id'] ?? null;
    $es_viable = $_POST['es_viable'] ?? null;

    if ($action === 'actualizar_viabilidad' && $id && $es_viable !== null) {
        try {
            $success = ClientePotencialServicio::actualizar_viabilidad((int)$id, (int)$es_viable, $conexion);
            if ($success) {
                echo json_encode(['success' => true, 'message' => 'Viabilidad actualizada correctamente.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'No se pudo actualizar la viabilidad.']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}