<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class ClientePotencialServicio
{
    // --- Atributos ---
    private ?int $id = null;
    private ?int $id_cliente_potencial = null;
    private ?int $id_servicio = null;
    private ?int $es_viable = null;
    private ?string $nombre_cliente_potencial = null;
    private ?string $nombre_servicio = null;

    /**
     * Constructor: Inicializa las propiedades del objeto ClientePotencialServicio.
     */
    public function __construct()
    {
        $this->id = 0;
        $this->id_cliente_potencial = null;
        $this->id_servicio = null;
        $this->es_viable = 0;     // Default to not viable
        $this->nombre_cliente_potencial = null;
        $this->nombre_servicio = null;
    }

    /**
     * Método estático para construir un objeto ClientePotencialServicio desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del servicio del cliente potencial.
     *
     * @return self Instancia de ClientePotencialServicio.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto = new self();
            $objeto->id = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_cliente_potencial = isset($resultado['id_cliente_potencial']) ? (int)$resultado['id_cliente_potencial'] : null;
            $objeto->id_servicio = isset($resultado['id_servicio']) ? (int)$resultado['id_servicio'] : null;
            $objeto->es_viable = isset($resultado['es_viable']) ? (int)$resultado['es_viable'] : 0;
            $objeto->nombre_cliente_potencial = $resultado['nombre_cliente_potencial'] ?? null;
            $objeto->nombre_servicio = $resultado['nombre_servicio'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir ClientePotencialServicio: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene un registro por su ID.
     *
     * @param int $id ID del registro.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto ClientePotencialServicio o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_id(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                cps.*,
                cp.nombre_empresa AS nombre_cliente_potencial,
                s.descripcion AS nombre_servicio
            FROM clientes_potenciales_servicios cps
            JOIN clientes_potenciales cp ON cps.id_cliente_potencial = cp.id
            JOIN servicios s ON cps.id_servicio = s.id
            WHERE
                cps.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener ClientePotencialServicio (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene servicios por cliente potencial.
     *
     * @param int $id_cliente_potencial ID del cliente potencial.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos ClientePotencialServicio.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_cliente_potencial(int $id_cliente_potencial, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                cps.*,
                cp.nombre_empresa AS nombre_cliente_potencial,
                s.descripcion AS nombre_servicio
            FROM clientes_potenciales_servicios cps
            JOIN clientes_potenciales cp ON cps.id_cliente_potencial = cp.id
            JOIN servicios s ON cps.id_servicio = s.id
            WHERE
                cps.id_cliente_potencial = :id_cliente_potencial
            ORDER BY
                cps.id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente_potencial', $id_cliente_potencial, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener servicios del cliente potencial: " . $e->getMessage());
        }
    }

    /**
     * Obtiene clientes potenciales por servicio.
     *
     * @param int $id_servicio ID del servicio.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos ClientePotencialServicio.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_servicio(int $id_servicio, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                cps.*,
                cp.nombre_empresa AS nombre_cliente_potencial,
                s.descripcion AS nombre_servicio
            FROM clientes_potenciales_servicios cps
            JOIN clientes_potenciales cp ON cps.id_cliente_potencial = cp.id
            JOIN servicios s ON cps.id_servicio = s.id
            WHERE
                cps.id_servicio = :id_servicio
            ORDER BY
                cps.id_cliente_potencial
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener clientes potenciales del servicio: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los registros.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos ClientePotencialServicio.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                cps.*,
                cp.nombre_empresa AS nombre_cliente_potencial,
                s.descripcion AS nombre_servicio
            FROM clientes_potenciales_servicios cps
            JOIN clientes_potenciales cp ON cps.id_cliente_potencial = cp.id
            JOIN servicios s ON cps.id_servicio = s.id
            ORDER BY
                cps.id_cliente_potencial, cps.id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de ClientePotencialServicio: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo registro en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo registro creado o false en caso de error.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        if ($this->id_cliente_potencial === null || $this->id_servicio === null) {
            throw new Exception("ID cliente potencial e ID servicio son requeridos para crear el registro.");
        }

        try {
            $query = <<<SQL
            INSERT INTO clientes_potenciales_servicios (
                 id_cliente_potencial
                ,id_servicio
                ,es_viable
            ) VALUES (
                 :id_cliente_potencial
                ,:id_servicio
                ,:es_viable
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_cliente_potencial', $this->getId_cliente_potencial(), PDO::PARAM_INT);
            $statement->bindValue(':id_servicio', $this->getId_servicio(), PDO::PARAM_INT);
            $statement->bindValue(':es_viable', $this->getEs_viable(), PDO::PARAM_INT);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear ClientePotencialServicio: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear ClientePotencialServicio: " . $e->getMessage());
        }
    }

    /**
     * Actualiza un registro existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    function actualizar(PDO $conexion): bool
    {
        if ($this->id === null || $this->id === 0) {
            throw new Exception("ID es requerido para actualizar el registro.");
        }

        try {
            $query = <<<SQL
            UPDATE clientes_potenciales_servicios SET
                 id_cliente_potencial = :id_cliente_potencial
                ,id_servicio = :id_servicio
                ,es_viable = :es_viable
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente_potencial', $this->getId_cliente_potencial(), PDO::PARAM_INT);
            $statement->bindValue(':id_servicio', $this->getId_servicio(), PDO::PARAM_INT);
            $statement->bindValue(':es_viable', $this->getEs_viable(), PDO::PARAM_INT);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar ClientePotencialServicio: " . $e->getMessage());
        }
    }

    /**
     * Elimina un registro por su ID.
     *
     * @param int $id ID del registro a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM clientes_potenciales_servicios
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar ClientePotencialServicio (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_cliente_potencial(): ?int
    {
        return $this->id_cliente_potencial;
    }

    public function setId_cliente_potencial(?int $id_cliente_potencial): self
    {
        $this->id_cliente_potencial = $id_cliente_potencial;
        return $this;
    }

    public function getId_servicio(): ?int
    {
        return $this->id_servicio;
    }

    public function setId_servicio(?int $id_servicio): self
    {
        $this->id_servicio = $id_servicio;
        return $this;
    }

    public function getEs_viable(): ?int
    {
        return $this->es_viable;
    }

    public function setEs_viable(?int $es_viable): self
    {
        $this->es_viable = $es_viable;
        return $this;
    }

    public function getNombre_cliente_potencial(): ?string
    {
        return $this->nombre_cliente_potencial;
    }

    public function setNombre_cliente_potencial(?string $nombre_cliente_potencial): self
    {
        $this->nombre_cliente_potencial = $nombre_cliente_potencial;
        return $this;
    }

    public function getNombre_servicio(): ?string
    {
        return $this->nombre_servicio;
    }

    public function setNombre_servicio(?string $nombre_servicio): self
    {
        $this->nombre_servicio = $nombre_servicio;
        return $this;
    }

    /**
     * Elimina todas las asociaciones de servicios para un cliente potencial específico.
     *
     * @param int $id_cliente_potencial ID del cliente potencial.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar_por_cliente_potencial(int $id_cliente_potencial, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM clientes_potenciales_servicios
            WHERE
                id_cliente_potencial = :id_cliente_potencial
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente_potencial', $id_cliente_potencial, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar servicios del cliente potencial (ID: $id_cliente_potencial): " . $e->getMessage());
        }
    }

    /**
     * Guarda servicios seleccionados para un cliente potencial específico.
     * Versión optimizada usando batch insert para mejor rendimiento.
     *
     * @param int $id_cliente_potencial ID del cliente potencial.
     * @param PDO $conexion Conexión PDO.
     * @param array $servicios_ids Array de IDs de servicios a asociar con el cliente.
     * @return bool True en éxito, false en fallo.
     * @throws Exception Si hay error durante la operación de base de datos.
     */
    public static function guardar_servicios_para_cliente(int $id_cliente_potencial, PDO $conexion, array $servicios_ids): bool
    {
        if ($id_cliente_potencial <= 0) {
            throw new Exception("Cliente Potencial ID debe ser válido antes de asociar servicios.");
        }

        if (empty($servicios_ids)) {
            throw new Exception("Al menos un servicio debe ser seleccionado.");
        }

        try {
            // Primero, eliminar asociaciones existentes de servicios para este cliente
            self::eliminar_por_cliente_potencial($id_cliente_potencial, $conexion);

            // Optimizado: Usar batch insert con múltiples cláusulas VALUES
            $placeholders = [];
            $values = [];

            foreach ($servicios_ids as $index => $servicio_id) {
                $placeholders[] = "(:id_cliente_potencial_{$index}, :id_servicio_{$index})";
                $values["id_cliente_potencial_{$index}"] = $id_cliente_potencial;
                $values["id_servicio_{$index}"] = (int)$servicio_id;
            }

            $insertQuery = "INSERT INTO clientes_potenciales_servicios (id_cliente_potencial, id_servicio) VALUES " . implode(', ', $placeholders);
            $insertStatement = $conexion->prepare($insertQuery);

            // Bind todos los valores de una vez
            foreach ($values as $param => $value) {
                $insertStatement->bindValue(":{$param}", $value, PDO::PARAM_INT);
            }

            if (!$insertStatement->execute()) {
                throw new Exception("Falló al guardar asociaciones de servicios en lote.");
            }

            return true;

        } catch (PDOException $e) {
            error_log("Error de base de datos guardando servicios para ClientePotencial ID {$id_cliente_potencial}: " . $e->getMessage());
            throw new Exception("Error de base de datos guardando servicios: " . $e->getMessage());
        }
    }

    /**
     * Obtiene los IDs de servicios asociados con un cliente potencial específico.
     *
     * @param int $id_cliente_potencial ID del cliente potencial.
     * @param PDO $conexion Conexión PDO.
     * @return array Array de IDs de servicios asociados con el cliente.
     * @throws Exception Si hay error durante la operación de base de datos.
     */
    public static function obtener_servicios_de_cliente(int $id_cliente_potencial, PDO $conexion): array
    {
        if ($id_cliente_potencial <= 0) {
            return [];
        }

        try {
            $query = <<<SQL
            SELECT *
            FROM clientes_potenciales_servicios
            WHERE id_cliente_potencial = :id_cliente_potencial
            ORDER BY id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente_potencial', $id_cliente_potencial, PDO::PARAM_INT);
            $statement->execute();

            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Error de base de datos obteniendo servicios para ClientePotencial ID {$id_cliente_potencial}: " . $e->getMessage());
            throw new Exception("Error de base de datos obteniendo servicios: " . $e->getMessage());
        }
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el servicio es viable para el cliente potencial.
     * @return bool
     */
    public function isViable(): bool
    {
        return $this->es_viable === 1;
    }

    public static function actualizar_viabilidad(int $id, int $es_viable, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE clientes_potenciales_servicios SET
                es_viable = :es_viable
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':es_viable', $es_viable, PDO::PARAM_INT);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar la viabilidad del servicio: " . $e->getMessage());
        }
    }
}
