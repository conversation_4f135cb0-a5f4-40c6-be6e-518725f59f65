<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class PaisCiudad
{
	// --- Atributos ---
	private ?int    $id     = null;
	private ?string $pais   = null;
	private ?string $ciudad = null;
	private ?int    $estado = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto.
	 */
	public function __construct()
	{
		$this->id     = 0;
		$this->pais   = null;
		$this->ciudad = null;
		$this->estado = 1; // Estado activo por defecto
	}
	
	/**
	 * Método estático para construir un objeto PaisesCiudades desde un array.
	 *
	 * @param array $data Array asociativo con los datos.
	 *
	 * @return self Instancia de PaisesCiudades.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $data = []): self
	{
		try {
			$objeto         = new self();
			$objeto->id     = isset($data['id']) ? (int)$data['id'] : 0;
			$objeto->pais   = $data['pais'] ?? null;
			$objeto->ciudad = $data['ciudad'] ?? null;
			$objeto->estado = isset($data['estado']) ? (int)$data['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir PaisesCiudades: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un registro por su ID.
	 *
	 * @param int $id       ID del registro.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto PaisesCiudades o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT * FROM paises_ciudades WHERE id = :id LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener PaisesCiudades (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un registro por su ID (método estándar lunex).
	 *
	 * @param int $id       ID del registro.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto PaisesCiudades o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_id(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM paises_ciudades
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener PaisesCiudades por ID: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de todos los países y ciudades.
	 *
	 * @param PDO  $conexion    Conexión PDO.
	 * @param bool $soloActivos True para obtener solo registros activos.
	 *
	 * @return array Array de objetos PaisesCiudades.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$sqlWhere = $soloActivos ? "WHERE estado = 1" : "";
			$query    = <<<SQL
            SELECT * FROM paises_ciudades {$sqlWhere} ORDER BY pais, ciudad
            SQL;
			
			$statement  = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de PaisesCiudades: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de países únicos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de strings con los nombres de los países.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_paises(PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT DISTINCT pais FROM paises_ciudades WHERE estado = 1 ORDER BY pais
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			return $statement->fetchAll(PDO::FETCH_COLUMN);
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener la lista de países: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de ciudades para un país específico.
	 *
	 * @param string $pais     Nombre del país.
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return array Array de strings con los nombres de las ciudades.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_ciudades_por_pais(string $pais, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT ciudad FROM paises_ciudades WHERE pais = :pais AND estado = 1 ORDER BY ciudad
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':pais', $pais, PDO::PARAM_STR);
			$statement->execute();
			return $statement->fetchAll(PDO::FETCH_COLUMN);
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener ciudades para el país '$pais': " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo registro de país-ciudad en la base de datos a partir de un objeto PaisesCiudades.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo registro creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getPais()) || empty($this->getCiudad())) {
			throw new Exception("País y ciudad son requeridos en el objeto PaisesCiudades para crearlo.");
		}

		try {
			// Preparar la consulta INSERT
			$query = <<<SQL
            INSERT INTO paises_ciudades (
            	 pais
            	,ciudad
            	,estado
            ) VALUES (
            	 :pais
            	,:ciudad
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':pais', mb_strtoupper($this->getPais()), PDO::PARAM_STR);
			$statement->bindValue(':ciudad', mb_strtoupper($this->getCiudad()), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del registro recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. combinación país-ciudad duplicada)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear país-ciudad: La combinación '" . $this->getPais() . " - " . $this->getCiudad() . "' ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear país-ciudad: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear país-ciudad: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un registro existente de país-ciudad en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	function actualizar(PDO $conexion): bool
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getId()) || empty($this->getPais()) || empty($this->getCiudad())) {
			throw new Exception("ID, país y ciudad son requeridos para actualizar el registro.");
		}

		try {
			// Preparar la consulta UPDATE
			$query = <<<SQL
            UPDATE paises_ciudades SET
            	 pais = :pais_update
            	,ciudad = :ciudad_update
            	,estado = :estado_update
            WHERE
            	id = :id_update
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':pais_update', mb_strtoupper($this->getPais()), PDO::PARAM_STR);
			$statement->bindValue(':ciudad_update', mb_strtoupper($this->getCiudad()), PDO::PARAM_STR);
			$statement->bindValue(':estado_update', $this->getEstado() ?? 1, PDO::PARAM_INT);
			$statement->bindValue(':id_update', $this->getId(), PDO::PARAM_INT);

			// Ejecutar la consulta
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			if ($e->getCode() == 23000 || $e->getCode() == 1062) {
				throw new Exception("Error al actualizar país-ciudad: La combinación '" . $this->getPais() . " - " . $this->getCiudad() . "' ya existe.");
			} else {
				throw new Exception("Error de base de datos al actualizar país-ciudad (ID: " . $this->getId() . "): " . $e->getMessage());
			}
		} catch (Exception $e) {
			throw new Exception("Error al actualizar país-ciudad: " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getPais(): ?string
	{
		return $this->pais;
	}
	
	public function setPais(?string $pais): self
	{
		$this->pais = $pais;
		return $this;
	}
	
	public function getCiudad(): ?string
	{
		return $this->ciudad;
	}
	
	public function setCiudad(?string $ciudad): self
	{
		$this->ciudad = $ciudad;
		return $this;
	}
	
	public function getEstado(): ?int
	{
		return $this->estado;
	}
	
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Elimina un registro de país-ciudad de la base de datos (eliminación física).
	 *
	 * @param int $id       ID del registro a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			$query = <<<SQL
            DELETE FROM paises_ciudades
            WHERE id = :id_delete
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_delete', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar país-ciudad (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un registro de país-ciudad estableciendo su estado a 0.
	 *
	 * @param int $id       ID del registro a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE paises_ciudades SET
            	estado = 0
            WHERE
            	id = :id_desactivar
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_desactivar', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar país-ciudad (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el registro está activo.
	 * @return bool
	 */
	public function isActiva(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
}
